﻿﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Bootstrapper.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Application bootstrapper responsible for initializing the application
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Eras2AmwApp.Common.Interfaces;
using Eras2AmwApp.Database.Contexts;
using Eras2AmwApp.Database.Interfaces;
using Eras2AmwApp.MAUI.Interfaces;
using Eras2AmwApp.MAUI.ViewModels;
using Microsoft.EntityFrameworkCore;
using ILogger = Serilog.ILogger;

namespace Eras2AmwApp.MAUI.Bootstrap
{
    public class Bootstrapper
    {
        private readonly IDbContextFactory contextFactory;
        private readonly IAppSettings appSettings;
        private readonly ILogger logger;
        private readonly IEcNavigationService navigationService;

        public Bootstrapper(
            IDbContextFactory contextFactory,
            IAppSettings appSettings,
            ILogger logger,
            IEcNavigationService navigationService)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this.navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
        }

        public async Task InitializeAsync()
        {
            try
            {
                // Setup Syncfusion
                SetupSyncfusion();

                // Initialize database
                InitializeDatabase();

                // Setup UI
                await SetupUiAsync();

                // Setup unhandled exceptions
                SetupUnhandledExceptions();
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Application initialization failed");
                throw;
            }
        }

        private void SetupSyncfusion()
        {
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(appSettings.SyncfusionLicenceKey);
        }


        //private void CreateFileSystem()
        //{
        //    try
        //    {
        //        // Ensure all required directories exist
        //        EnsureDirectoryExists(appSettings.DatabaseDirectory);
        //        EnsureDirectoryExists(appSettings.LogFilesDirectory);
        //        EnsureDirectoryExists(appSettings.WebserviceDirectory);
        //        EnsureDirectoryExists(appSettings.WebserviceUploadDirectory);
        //        EnsureDirectoryExists(appSettings.WebserviceDownloadDirectory);
        //        EnsureDirectoryExists(appSettings.PicturesDirectory);
        //        EnsureDirectoryExists(appSettings.SignaturesDirectory);
        //        EnsureDirectoryExists(appSettings.BackupDirectory);
        //    }
        //    catch (Exception)
        //    {
        //        throw;
        //    }
        //}

        private void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }

        private void InitializeDatabase()
        {
            try
            {
                // Initialize App database
                InitializeAppDatabase();

                // Initialize AMW database
                InitializeAmwDatabase();

                // Initialize AdminTest database (Copy from Android assets to database folder)
                InitializeAdminTestDatabase();
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Database initialization failed");
                throw;
            }
        }

        private void InitializeAppDatabase()
        {
            string dbFilePath = Path.Combine(appSettings.DatabaseDirectory, "app.sqlite3");
            if (!File.Exists(dbFilePath))
            {
                using var context = contextFactory.CreateApp();
                context.Database.Migrate();
            }
        }

        private void InitializeAmwDatabase()
        {
            string dbFilePath = Path.Combine(appSettings.DatabaseDirectory, "eras2_amw.sqlite3");
            if (!File.Exists(dbFilePath))
            {
                using var context = contextFactory.CreateAmw();
                context.Database.Migrate();
            }
        }

        private void InitializeAdminTestDatabase()
        {
            string dbFileName = "test_eras2_amw.sqlite3";
            string targetPath = Path.Combine(appSettings.DatabaseDirectory, dbFileName);

            if (!Directory.Exists(appSettings.DatabaseDirectory))
            {
                Directory.CreateDirectory(appSettings.DatabaseDirectory);
            }

            if (!File.Exists(targetPath))
            {
                using var assetStream = appSettings.GetAssetsTestStream();
                using var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write);
                assetStream.CopyTo(fileStream);
                
            }
        }

        private async Task SetupUiAsync()
        {
            try
            {
                // Get the database directory path
                string dbDirectory = appSettings.DatabaseDirectory;
                string dbFilePath = Path.Combine(dbDirectory, "app.sqlite3");

                // Check if any customers exist in the database
                bool customersExist = CheckIfCustomersExist();

                // Check if the database file exists
                bool databaseExists = File.Exists(dbFilePath);

                // Navigate to the appropriate starting page
                if(customersExist)
                {
                    await Shell.Current.GoToAsync("//LoginPage");
                }
                else
                {
                    await Shell.Current.GoToAsync("//RegistrationPage");
                }

            }
            catch
            {
                throw;
            }
        }

        private bool CheckIfCustomersExist()
        {
            try
            {
                using var context = contextFactory.CreateApp();
                bool hasCustomers = context.Customers.Any();
                return hasCustomers;
            }
            catch
            {
                // If there's an error checking customers, assume none exist
                return false;
            }
        }

        private void SetupUnhandledExceptions()
        {
            AppDomain.CurrentDomain.UnhandledException += CurrentDomainOnUnhandledException;
            TaskScheduler.UnobservedTaskException += TaskSchedulerOnUnobservedTaskException;
        }

        private void TaskSchedulerOnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs unobservedTaskExceptionEventArgs)
        {
            // Handle unobserved task exception
        }

        private void CurrentDomainOnUnhandledException(object? sender, UnhandledExceptionEventArgs unhandledExceptionEventArgs)
        {
            // Handle unhandled exception
        }
    }
}
