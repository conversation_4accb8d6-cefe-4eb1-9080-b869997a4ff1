﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AbstractContext.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Contexts
{
    using System;
    using System.IO;
    using Common.Interfaces;

    using Eras2AmwApp.Common.Implementations;

    using Microsoft.Data.Sqlite;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Logging.Console;
    using Microsoft.Extensions.DependencyInjection;

    public abstract class AbstractContext : DbContext
    {
        protected readonly IAppSettings appSettings;

        private static LoggerFactory myLoggerFactory;

        protected AbstractContext(IAppSettings appSettings)
        {
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        public static LoggerFactory MyLoggerFactory
        {
            get
            {
                if (myLoggerFactory == null)
                {
                    // Create a new LoggerFactory with appropriate configuration
                    var serviceCollection = new ServiceCollection();

                    // Configure logging
                    serviceCollection.AddLogging(builder =>
                    {
#if DEBUG
                        // Add console logger with filter for database commands with error level
                        builder.AddConsole(options =>
                        {
                            options.LogToStandardErrorThreshold = LogLevel.Error;
                        });
                        builder.AddFilter("Microsoft.EntityFrameworkCore.Database.Command", LogLevel.Error);
#endif

#if !DEBUG
                        // In release mode, use a simple logger
                        builder.AddProvider(new SimpleLoggerProvider((category, level) =>
                            category == DbLoggerCategory.Database.Command.Name && level == LogLevel.Error));
#endif
                    });

                    // Build the service provider and get the logger factory
                    var serviceProvider = serviceCollection.BuildServiceProvider();
                    myLoggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>() as LoggerFactory;
                }

                return myLoggerFactory;
            }
        }

        protected virtual string DatabaseName { get; set; }

        protected string DatabasePath => Path.Combine(appSettings.DatabaseDirectory, DatabaseName);

        public void RecreateDatabase()
        {
            DeleteDatabase();
            Database.Migrate();
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (optionsBuilder == null)
            {
                throw new ArgumentNullException(nameof(optionsBuilder));
            }

            SqliteConnection connection = InitializeSQLiteConnection();

            // Open the connection to run the PRAGMA
            connection.Open();

            using (var command = connection.CreateCommand())
            {
                command.CommandText = "PRAGMA journal_mode=DELETE;";
                command.CommandText = "PRAGMA foreign_keys = ON;";
                command.ExecuteNonQuery();
            }

            optionsBuilder
                .UseLoggerFactory(MyLoggerFactory)
                .EnableSensitiveDataLogging()
                .UseSqlite(connection);
        }

        private void DeleteDatabase()
        {
            if (File.Exists(DatabasePath))
            {
                File.Delete(DatabasePath);
            }
        }

        private SqliteConnection InitializeSQLiteConnection()
        {
            // Ensure the database directory exists
            EnsureDatabaseDirectoryExists();

            var connection = new SqliteConnection($"DataSource={DatabasePath}");

            return connection;
        }

        private void EnsureDatabaseDirectoryExists()
        {
            string databaseDirectory = appSettings.DatabaseDirectory;
            if (!Directory.Exists(databaseDirectory))
            {
                Directory.CreateDirectory(databaseDirectory);
            }
        }
    }
}