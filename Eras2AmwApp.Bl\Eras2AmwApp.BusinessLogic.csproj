﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;Release;StandaloneDevelopment;Development;StandaloneRelease</Configurations>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneDevelopment|AnyCPU'">
    <DefineConstants>DEVELOPMENT,STANDALONE_APP</DefineConstants>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|AnyCPU'">
    <DefineConstants>DEVELOPMENT</DefineConstants>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneRelease|AnyCPU'">
    <Optimize>true</Optimize>
    <DefineConstants>TRACE;STANDALONE_APP</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Automapper\**" />
    <Compile Remove="Domain\Extensions\**" />
    <Compile Remove="Implementations\**" />
    <Compile Remove="obj\**\*.cs" />
    <EmbeddedResource Remove="Automapper\**" />
    <EmbeddedResource Remove="Domain\Extensions\**" />
    <EmbeddedResource Remove="Implementations\**" />
    <None Remove="Automapper\**" />
    <None Remove="Domain\Extensions\**" />
    <None Remove="Implementations\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Polly" Version="8.5.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Endiancode.Utilities\Endiancode.Utilities.csproj" />
    <ProjectReference Include="..\Eras2AmwApp.Common\Eras2AmwApp.Common.csproj" />
    <ProjectReference Include="..\Eras2AmwApp.Database\Eras2AmwApp.Database.csproj" />
    <ProjectReference Include="..\Eras2AmwApp.WebService\Eras2AmwApp.WebService.csproj" />
  </ItemGroup>

</Project>
