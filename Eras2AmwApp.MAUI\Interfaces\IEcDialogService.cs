//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IEcDialogService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Dialog Service Interface for MAUI
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System.Threading.Tasks;
using Eras2AmwApp.MAUI.Enums;
using Eras2AmwApp.MAUI.Models;

namespace Eras2AmwApp.MAUI.Interfaces
{
    public interface IEcDialogService
    {
        void Info(string message, string? title = null);

        Task<DialogResponse> AcceptAsync(string message, string? acceptButtonText = null, string? title = null);

        Task<DialogResponse> AcceptDeclineAsync(string message, string acceptButtonText, string declineButtonText, string? title = null);

        void ShowBusyIndicator(AnimationTypes animationType = AnimationTypes.Gear, string title = "");

        void SetBusyIndicatorText(string text);

        void HideBusyIndicator();

        Task ShowErrorAsync(string title, string message);

        void ShowError(string title, string message);
    }
}
