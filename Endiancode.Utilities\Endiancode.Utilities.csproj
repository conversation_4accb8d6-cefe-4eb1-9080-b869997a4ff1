<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;Release;StandaloneDevelopment;Development;StandaloneRelease</Configurations>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|AnyCPU'">
    <Optimize>true</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneRelease|AnyCPU'">
    <Optimize>true</Optimize>
    <DefineConstants>TRACE;STANDALONE_APP</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneDevelopment|AnyCPU'">
    <Optimize>true</Optimize>
    <DefineConstants>TRACE;STANDALONE_APP</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Interfaces\**" />
    <EmbeddedResource Remove="Interfaces\**" />
    <None Remove="Interfaces\**" />
  </ItemGroup>

</Project>
