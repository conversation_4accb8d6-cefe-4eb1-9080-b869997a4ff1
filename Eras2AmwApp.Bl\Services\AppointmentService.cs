//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Microsoft.EntityFrameworkCore.Query;

    public class AppointmentService : IAppointmentService
    {
        private readonly IDbContextFactory contextFactory;

        public AppointmentService(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public Appointment GetAppointmentDateOrder(DateTime date, Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            // DateTime is a value type and cannot be null

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return GetAppointmentInclude(context).Single(x => x.From == date && x.OrderGuid == order.Guid);
            }
        }

        public Appointment GetAppointment(Guid appointmentGuid)
        {
            // Guid is a value type and cannot be null

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return GetAppointmentInclude(context).SingleOrDefault(x => x.Guid == appointmentGuid);
            }
        }

        public Appointment GetAppointmentWithNutzeinheitAddresses(Guid appointmentGuid)
        {
            // Guid is a value type and cannot be null

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Appointments.Include(Appointments => Appointments.Order)
                                           .ThenInclude(Order => Order.Abrechnungseinheit)
                                           .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Address)
                                           .Include(Appointments => Appointments.Order)
                                           .ThenInclude(Order => Order.Abrechnungseinheit)
                                           .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Nutzeinheiten)
                                           .ThenInclude(Nutzeinheiten => Nutzeinheiten.Address).Single(x => x.Guid == appointmentGuid);
            }
        }

        private IIncludableQueryable<Appointment, CommunicationFeature> GetAppointmentInclude(Eras2AmwContext context)
        {
            return context.Appointments
                .Include(Appointments => Appointments.AppointmentNutzeinheiten)
                .ThenInclude(AppointmentNutzeinheiten => AppointmentNutzeinheiten.Nutzeinheit)
                .ThenInclude(Nutzeinheit => Nutzeinheit.Abrechnungseinheit)

                .Include(Appointments => Appointments.AppointmentNutzeinheiten)
                .ThenInclude(AppointmentNutzeinheiten => AppointmentNutzeinheiten.Nutzeinheit)
                .ThenInclude(Nutzeinheit => Nutzeinheit.OrderStates)

                .Include(Appointments => Appointments.AppointmentNutzeinheiten)
                .ThenInclude(AppointmentNutzeinheiten => AppointmentNutzeinheiten.Nutzeinheit)
                .ThenInclude(Nutzeinheit => Nutzeinheit.Address)

                .Include(Appointments => Appointments.AppointmentNutzeinheiten)
                .ThenInclude(AppointmentNutzeinheiten => AppointmentNutzeinheiten.Nutzeinheit)
                .ThenInclude(Nutzeinheit => Nutzeinheit.Devices)
                .ThenInclude(Device => Device.OrderStates)

                .Include(Appointments => Appointments.AppointmentNutzeinheiten)
                .ThenInclude(AppointmentNutzeinheiten => AppointmentNutzeinheiten.Nutzeinheit)
                .ThenInclude(Nutzeinheit => Nutzeinheit.NutzeinheitOrderPositions)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Address)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.OrderState)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.OrderPositions)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Nutzeinheiten)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.PersonAbrechnungseinheiten)
                .ThenInclude(PersonAbrechnungseinheiten => PersonAbrechnungseinheiten.Person)
                .ThenInclude(Person => Person.Salutation)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.PersonAbrechnungseinheiten)
                .ThenInclude(PersonAbrechnungseinheiten => PersonAbrechnungseinheiten.Person)
                .ThenInclude(Person => Person.Title)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.PersonAbrechnungseinheiten)
                .ThenInclude(PersonAbrechnungseinheiten => PersonAbrechnungseinheiten.Person)
                .ThenInclude(Person => Person.PersonCommunications)
                .ThenInclude(PersonCommunications => PersonCommunications.CommunicationFeature)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Nutzeinheiten)
                .ThenInclude(Nutzeinheiten => Nutzeinheiten.Address)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Nutzeinheiten)
                .ThenInclude(Nutzeinheiten => Nutzeinheiten.Signatures)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Nutzeinheiten)
                .ThenInclude(Nutzeinheiten => Nutzeinheiten.Nutzer)
                .ThenInclude(Nutzer => Nutzer.Salutation)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Nutzeinheiten)
                .ThenInclude(Nutzeinheiten => Nutzeinheiten.Nutzer)
                .ThenInclude(Nutzer => Nutzer.Title)

                .Include(Appointments => Appointments.Order)
                .ThenInclude(Order => Order.Abrechnungseinheit)
                .ThenInclude(Abrechnungseinheit => Abrechnungseinheit.Nutzeinheiten)
                .ThenInclude(Nutzeinheiten => Nutzeinheiten.Nutzer)
                .ThenInclude(Nutzer => Nutzer.NutzerCommunications)
                .ThenInclude(NutzerCommunications => NutzerCommunications.CommunicationFeature);
        }

        public List<AppointmentTechnician> GetTechnitianAppointments(Guid technicianGuid)
        {
            using (var context = contextFactory.CreateAmw())
            {
                var list = GetTechnicianAppointmentInclude(context).ToList();
                list = list.Where(x => x.TechnicianGuid == technicianGuid).ToList();
                return list;
            }
        }

        private IQueryable<AppointmentTechnician> GetTechnicianAppointmentInclude(Eras2AmwContext context)
        {
            return context.AppointmentUsers
                .AsSplitQuery()
                .Include(x => x.Appointment)
                    .ThenInclude(x => x.Order)
                        .ThenInclude(x => x.OrderState)

                .Include(x => x.Appointment)
                    .ThenInclude(x => x.AppointmentNutzeinheiten)
                        .ThenInclude(x => x.Nutzeinheit)
                            .ThenInclude(x => x.Devices)

                .Include(x => x.Appointment)
                    .ThenInclude(x => x.AppointmentNutzeinheiten)
                        .ThenInclude(x => x.Nutzeinheit)
                            .ThenInclude(x => x.OrderStates)

                .Include(x => x.Appointment)
                    .ThenInclude(x => x.AppointmentNutzeinheiten)
                        .ThenInclude(x => x.Nutzeinheit)
                            .ThenInclude(x => x.Nutzer)

                .Include(x => x.Appointment)
                    .ThenInclude(x => x.Order)
                        .ThenInclude(x => x.Abrechnungseinheit)
                            .ThenInclude(x => x.Address)

                .Include(x => x.Appointment)
                    .ThenInclude(x => x.Order)
                        .ThenInclude(x => x.Abrechnungseinheit)
                            .ThenInclude(x => x.Nutzeinheiten)
                                .ThenInclude(x => x.Address)

                .Include(x => x.Appointment)
                    .ThenInclude(x => x.Order)
                        .ThenInclude(x => x.Abrechnungseinheit)
                            .ThenInclude(x => x.Nutzeinheiten)
                                .ThenInclude(x => x.Nutzer);
        }

        public List<AppointmentTechnician> GetTechnitianAppointments()
        {
            using (var context = contextFactory.CreateAmw())
            {
                return context.AppointmentUsers
                    .Include(x => x.Appointment)
                    .ThenInclude(x => x.Order).ToList();
            }
        }

        public void SaveAppointmentNutzeinheit(AppointmentNutzeinheit appointmentNutzeinheit)
        {
            if (appointmentNutzeinheit is null)
            {
                throw new ArgumentNullException(nameof(appointmentNutzeinheit));
            }

            using(var context = contextFactory.CreateAmw())
            {
                context.AppointmentNutzeinheiten.Add(appointmentNutzeinheit);
                context.SaveChanges();
            }
        }
    }
}

