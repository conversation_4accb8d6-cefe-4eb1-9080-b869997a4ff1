<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="Eras2AmwApp.MAUI.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Eras2AmwApp.MAUI"
    xmlns:views="clr-namespace:Eras2AmwApp.MAUI.Views"
    Shell.FlyoutBehavior="Disabled"
    Title="Eras2AmwApp.MAUI">

   
    <ShellContent
        ContentTemplate="{DataTemplate views:RegistrationPage}"
        Route="RegistrationPage"
        AutomationId="RegistrationPageShellContent" />

    <ShellContent
        ContentTemplate="{DataTemplate views:LoginPage}"
        Route="LoginPage"
        AutomationId="LoginPageShellContent" />

    <ShellContent
        ContentTemplate="{DataTemplate views:AppointmentPage}"
        Route="AppointmentPage"
        AutomationId="AppointmentPageShellContent" />

</Shell>
