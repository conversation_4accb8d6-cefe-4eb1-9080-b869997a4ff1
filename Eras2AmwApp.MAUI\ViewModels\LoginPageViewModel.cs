//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="LoginPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  MAUI implementation of the LoginPageViewModel
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.Domain.Eras2App.Database;
using Eras2AmwApp.MAUI.Enums;
using Eras2AmwApp.MAUI.Interfaces;
using Eras2AmwApp.WebService.Interfaces;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.Maui.Networking;
using IServiceLocator = Eras2AmwApp.Common.Interfaces.IServiceLocator;

namespace Eras2AmwApp.MAUI.ViewModels
{
    public partial class LoginPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IEcDialogService dialogService;
        private readonly ILoginService loginService;
        private readonly IValidator<LoginPageViewModel> validator;
        private readonly IAppDeviceInformationService appDeviceInformationService;
        private readonly IAmwWebservice webservice;

        #endregion

        #region properties

        [ObservableProperty]
        private string _username = string.Empty;

        [ObservableProperty]
        private string _password = string.Empty;

        [ObservableProperty]
        private string _appTitle = string.Empty;

        [ObservableProperty]
        private string _appVersion = string.Empty;

        [ObservableProperty]
        private string _webserviceUrl = string.Empty;

        [ObservableProperty]
        private string _loginErrorText = string.Empty;

        [ObservableProperty]
        private bool _usernameHasError;

        [ObservableProperty]
        private string _usernameErrorText = string.Empty;

        [ObservableProperty]
        private bool _passwordHasError;

        [ObservableProperty]
        private string _passwordErrorText = string.Empty;

        #endregion

        #region ctor

        public LoginPageViewModel(
            IServiceLocator serviceLocator,
            IEcDialogService dialogService,
            IEcNavigationService navigationService,
            ILoginService loginService,
            IValidator<LoginPageViewModel> validator,
            IAppDeviceInformationService appDeviceInformationService,
            IAmwWebservice webservice)
            : base(serviceLocator, navigationService)
        {
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
            this.validator = validator ?? throw new ArgumentNullException(nameof(validator));
            this.appDeviceInformationService = appDeviceInformationService ?? throw new ArgumentNullException(nameof(appDeviceInformationService));
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));

            InitializeProperties();
        }

        private void InitializeProperties()
        {
            Username = "AdminTest";//string.Empty;
            Password = "AdminTest!";//string.Empty;

            AppTitle = loginService.GetLoginAppTitle();
            AppVersion = "Version: 1.0"; // Simplified version

            try
            {
                Webservice appWebService = appDeviceInformationService.GetAppWebservice();

                if (appWebService != null)
                {
                    WebserviceUrl = appWebService.Url;
                    if (WebserviceUrl.Contains("servamw.eras-online.de"))
                    {
                        WebserviceUrl = WebserviceUrl.Substring(8, WebserviceUrl.Length - 31);
                    }
                    else if (WebserviceUrl.Contains("AdminTest"))
                    {
                        Username = "AdminTest";
                        Password = "AdminTest";
                    }
                    else
                    {
                        WebserviceUrl = WebserviceUrl.Substring(7, WebserviceUrl.Length - 7);
                    }
                }
                else
                {
                    // Default value if no webservice is configured
                    WebserviceUrl = "No webservice configured";
                    logger.Warning("No webservice configuration found");
                }
            }
            catch (Exception ex)
            {
                // Handle the exception
                WebserviceUrl = "Error retrieving webservice URL";
                logger.Error(ex, "Error retrieving webservice configuration");
            }

            // Register for connectivity changes
            Connectivity.ConnectivityChanged += Connectivity_ConnectivityChanged;

            // Set initial network access status
            UpdateNetworkAccessStatus();
        }

        private void Connectivity_ConnectivityChanged(object? sender, ConnectivityChangedEventArgs e)
        {
            // Update network access status when connectivity changes
            UpdateNetworkAccessStatus();
        }

        #endregion

        #region commands

        public IRelayCommand LoginCommand => new RelayCommand(async () => await LoginAsync());
        public IRelayCommand ResetUserCommand => new RelayCommand(ResetUser);
        public IRelayCommand WipeDatabaseCommand => new RelayCommand(WipeDatabase);
        public IRelayCommand AppearingCommand => new RelayCommand(OnAppearing);
        public IRelayCommand DisappearingCommand => new RelayCommand(OnDisappearing);

        #endregion

        #region methods

        private async Task LoginAsync()
        {
            try
            {
                // Update network access status in LoginService
                UpdateNetworkAccessStatus();

                // Check for internet connectivity
                //if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                //{
                //    LoginErrorText = "No internet connection. Please check your network and try again.";
                //    return;
                //}

                // Initialize the webservice if needed
                InitializeWebservice();

                // Clear previous error messages
                LoginErrorText = string.Empty;

                // Validate input
                ValidationResult validationResult = validator.Validate(this);
                UpdateErrorMessages(validationResult);

                if (!validationResult.IsValid)
                {
                    return;
                }

                // Show busy indicator
                //dialogService.ShowBusyIndicator(AnimationTypes.Gear, localisationService.Get("LoginInProgress"));
                dialogService.ShowBusyIndicator(AnimationTypes.Gear, "Log in!");
                try
                {
                    if (Username == "AdminTest" && Password == "AdminTest")
                    {
                        Guid adminUserGuid = await loginService.AdminTestLoginAsync(Username, Password);
                        await navigationService.NavigateToAsync<AppointmentPageViewModel>(adminUserGuid);
                        await navigationService.RemoveLastFromBackStackAsync();
                        return;
                    }

                    // Attempt login
                    await loginService.LoginAsync(Username.Trim(), Password.Trim());

                    // Navigate to main page on successful login
                    await navigationService.NavigateToAsync<AppointmentPageViewModel>();
                    await navigationService.RemoveBackStackAsync();
                }
                finally
                {
                    // Hide busy indicator
                    await Task.Delay(500);
                    dialogService.HideBusyIndicator();
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                logger.Error(ex, "Login failed due to unauthorized access");
                LoginErrorText = ex.Message;
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Login failed with exception");
                LoginErrorText = ex.Message;
            }
        }

        private void ResetUser()
        {
            try
            {
                loginService.DeleteUsers();
                navigationService.ShowInitPageOnResume = true;
                navigationService.NavigateToAsync<RegistrationPageViewModel>();
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error resetting user");
                LoginErrorText = ex.Message;
            }
        }

        private void WipeDatabase()
        {
            try
            {
                loginService.WipeDatabase();
                navigationService.ShowInitPageOnResume = true;
                navigationService.NavigateToAsync<RegistrationPageViewModel>();
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error wiping database");
                LoginErrorText = ex.Message;
            }
        }

        private void OnAppearing()
        {
            // Reset error messages when page appears
            LoginErrorText = string.Empty;
            UsernameHasError = false;
            UsernameErrorText = string.Empty;
            PasswordHasError = false;
            PasswordErrorText = string.Empty;

            // Check network connectivity and update LoginService
            UpdateNetworkAccessStatus();
        }

        private void OnDisappearing()
        {
            // Clean up when page disappears
            Username = string.Empty;
            Password = string.Empty;

            // Unregister connectivity event handler
            Connectivity.ConnectivityChanged -= Connectivity_ConnectivityChanged;
        }



        private void UpdateNetworkAccessStatus()
        {
            // Check if the device has internet connectivity
            bool hasInternetAccess = Connectivity.NetworkAccess == NetworkAccess.Internet;

            // Update the LoginService's NetworkAccess property
            loginService.NetworkAccess = hasInternetAccess;

            // Log the network status
            logger.Information("Network access status: {Status}", hasInternetAccess ? "Connected" : "Disconnected");
        }

        private void InitializeWebservice()
        {
            try
            {
                // Call the Start method directly on the webservice
                // This will initialize the client
                var amwWebservice = webservice as dynamic;
                amwWebservice.Start();
                logger.Information("Webservice initialized successfully");
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Failed to initialize webservice: {Message}", ex.Message);
                // Log but don't rethrow - we'll let the login attempt proceed and it will fail with a more specific error
            }
        }

        #endregion
    }
}
