//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NinjectModules.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Ioc
{
    using Implementations;
    using Interfaces;
    using Ninject.Modules;
    using Serilog;
    using Services;

    public class NinjectModules : NinjectModule
    {
        public override void Load()
        {
#if DEVELOPMENT
            BindDevelopmentServices();
#else
            BindReleaseServices();
#endif

            Bind<ISimpleFileLoggerFactory>().To<SimpleFileLoggerFactory>().InSingletonScope();

            Bind<IResourceService>().To<ResourceService>();
            Bind<ILogger>().ToMethod(CreateLogger);
            Bind<IServiceLocator>().To<ServiceLocator>();
            Bind<IBackupService>().To<BackupService>();
            Bind<INetworkServices>().To<NetworkServices>();
            Bind<IGlobalCacheTrash>().To<GlobalCacheTrash>().InSingletonScope();
        }

        private static ILogger CreateLogger(Ninject.Activation.IContext context)
        {
            var simpleFileLogger = NinjectKernel.Get<ISimpleFileLoggerFactory>();
            
            return simpleFileLogger.Create();
        }
    
        private void BindDevelopmentServices()
        {
            Bind<IAppSettings>().To<AppSettingsDevelopment>().InSingletonScope();
        }

        // ReSharper disable once UnusedMember.Local
        private void BindReleaseServices()
        {
            Bind<IAppSettings>().To<AppSettings>().InSingletonScope();
        }
    }
}